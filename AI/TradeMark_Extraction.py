# This script crops images using Google's Gemini Flash 2.0.
# The images are first processed by predefined functions and then
# further cropped using Google's Gemini Flash 2.0 API.
import cv2, time
import numpy as np

from AI.GC_VertexAI import vertex_genai_bounding_box

PROMPT = "Detect the 2d bounding boxes of the trademark image without any white space arround it (with “label” as trademark text if the trademark has text, else “none”)."

def image_to_sqaureImage(img: np.ndarray, impute_color) -> np.ndarray:
    """Takes an image and an RGB color array, and returns a square image with the specified color filling the empty space.

    Args:
        img (np.ndarray): The input image.
        impute_color (array): An array of length 3 representing the RGB color.

    Returns:
        np.ndarray: A transformed square image.
    """
    height, width, _ = img.shape
    square_size = int(max(height, width))

    square_image = np.full((square_size, square_size, 3), impute_color, dtype=np.uint8)

    # Find offsets to center the original image on top of the square image
    x_offset = (square_size - width) // 2
    y_offset = (square_size - height) // 2

    square_image[y_offset : y_offset + height, x_offset : x_offset + width] = img
    return square_image


def change_color_intensity(img: np.ndarray, change_val: int) -> np.ndarray:
    """Change the color intensity of the image by adding the specified change_val to each pixel of the image.

    Args:
        img (np.ndarray): The input image.
        change_val (int): The value to change the intensity by.

    Returns:
        np.ndarray: The image with changed color intensity.
    """
    img = img.astype(np.float32)

    white_mask = np.all(img == [255, 255, 255], axis=-1)
    img += change_val
    img = np.clip(img, 0, 255)
    img = img.astype(np.uint8)
    img[white_mask] = [255, 255, 255]
    return img


def crop_image_based_on_gemini_output(
    gemini_outputs: list, geini_input_img: np.ndarray, original_img: np.ndarray
) -> np.ndarray:
    """Takes in a list of outputs of the geini Flash 2.0 model and extracts the location in the image. The image will be
    cropped and saved in the specified location.

    Args:
        gemini_outputs (list): A list of dictionaries where each dictionary represents a defined location of the image.
            Each dictionary is going to have two keys: box_2d and label. The box_2d is always going to be in
            the following format [y_min, x_min, y_max, x_max]. Each value is normalized to 0 - 1000 for every image.
        geini_input_img (np.ndarray): The input image used for the geini model.
        original_img (np.ndarray): The original image to be cropped.

    Returns:
        np.ndarray: The cropped image.
    """
    if not gemini_outputs:
        raise ValueError("empty gemini_outputs")

    if len(gemini_outputs) != 1:
        raise RuntimeError("More than one output")

    norm_scaler = 1000
    original_img_height, original_img_width, _ = original_img.shape
    scaled_img_height, scaled_img_width, _ = geini_input_img.shape

    scaled_factor_height = original_img_height / scaled_img_height
    scaled_factor_width = original_img_width / scaled_img_width

    genimi_cor_dict = gemini_outputs[0]

    # load scaler
    scaler_y_min, scaler_x_min, scaler_y_max, scaler_x_max = (
        np.array(genimi_cor_dict["box_2d"]) / norm_scaler
    )

    # conver scaler to fit the image size
    x_min, y_min = (
        int(scaled_img_width * (scaler_x_min - 0.08) * scaled_factor_width),
        int(scaled_img_height * (scaler_y_min - 0.08) * scaled_factor_height),
    )
    x_max, y_max = (
        int(scaled_img_width * (scaler_x_max + 0.08) * scaled_factor_width),
        int(scaled_img_height * (scaler_y_max + 0.08) * scaled_factor_height),
    )

    # crop and save the image
    cropped_img = original_img[y_min:y_max, x_min:x_max]

    return cropped_img


def ai_tradeMark_cropping(img: np.ndarray) -> np.ndarray:
    """Crop the region of interest using Gemini Flash 2.0 API.

    Args:
        img (np.ndarray): The image that needs to be cropped.

    Returns:
        np.ndarray: The image that is cropped by AI.
    """
    # Transform the image
    square_img = image_to_sqaureImage(img, [255, 255, 255])
    square_img_darker = change_color_intensity(square_img, -150)

    # Shrink the image to 350 * 350
    # TODO: might want to swtich to 650 * 650, but it will give duplicates
    resized_square_img_darker = cv2.resize(square_img_darker, (640, 640))

    # Call the API
    trademark_locations = vertex_genai_bounding_box([("text", PROMPT), ("image_cv2", resized_square_img_darker)], image_url=None)

    # Transform the image back and crop the image
    return crop_image_based_on_gemini_output(
        trademark_locations, resized_square_img_darker, square_img
    )


if __name__ == "__main__":

    # input_folder = "/Users/<USER>/Documents/local_repo/TRO-USside/guoxuan_test"
    # output_folder = os.path.join(input_folder, "cropped_images")

    # if not os.path.exists(output_folder):
    #     os.makedirs(output_folder)

    # for filename in os.listdir(input_folder):
    #     if filename.endswith(".jpg") or filename.endswith(".png"):
    #         img_path = os.path.join(input_folder, filename)
    #         img = cv2.imread(img_path)
    #         cropped_img = ai_tradeMark_cropping(img)
    #         output_path = os.path.join(output_folder, filename)
    #         cv2.imwrite(output_path, cropped_img)
    print("Hi")
