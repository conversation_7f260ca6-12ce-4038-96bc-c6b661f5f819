import os, sys, cv2, shutil, re
from typing import Optional
sys.path.append(os.getcwd())
import numpy as np
from PIL import Image
from logdata import log_message
from Alerts.PicturesProcessing.ProcessPicturesShared import crop_white_space, keywords, assess_similarity_to_template_single_folder
from Alerts.PicturesProcessing.ExtractPictures import  extract_images_from_a_pdf_page
from AI.GC_VertexAI import vertex_genai_multi
from AI.GCV_GetImageParts import get_image_parts
from AI.LLM_shared import get_json, get_json_list
from langfuse import observe
import langfuse
from IP.Copyrights.Copyright_USCO import extract_formated_copyright_registration_number
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor # Added import for OCRProcessor
from typing import List
import Common.Constants as Constants

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)


@observe(capture_input=False, capture_output=False)
def process_copyright_exhibit(df, index, ref_hash, size, case_images_directory, case_directory_1, pdf_document, pdf_file, pages_text_json, certainty, ip_manager: IPTrackingManager, location_id: str):
    """
    Processes a potential copyright exhibit PDF to find copyright certificates and associated images.
    Updates IPTrackingManager based on findings.

    Returns:
        tuple: (bool, bool, list[str])
            - bool: True if copyright exhibit text (registration numbers) was found.
            - bool: True if distinct copyright artwork images were successfully linked to any of those registration numbers.
            - list[str]: A list of unique registration numbers textually identified in the exhibit.
    """
    log_message(f"        - Extracting images from the pdf for exhibit processing at {location_id}.")
    pdf_file_no_ext = os.path.splitext(pdf_file)[0]
    similar_images_in_pdf = [] # List of paths to certificate images
    identified_reg_nos = []
    identified_reg_nos_with_artwork = []
    
    # 1. Image template approach
    folder_path = os.path.join(case_directory_1, pdf_file_no_ext)
    log_message(f"        - Assessing similarity of images to copyright template")
    if os.path.isdir(folder_path):
        similar_images_in_pdf, differences, total_similar_images_folder = assess_similarity_to_template_single_folder(folder_path, ref_hash, size, 20)

    # 2. Keywords approach
    page_numbers_with_keywords = []
    if similar_images_in_pdf or certainty=="high" or ip_manager.is_goal_relevant('copyright'): # if it is the right document, we be easy on keywords
        for page_number in range(pdf_document.page_count):
            page_text = pages_text_json.get(page_number+1, "")
            if any(sum(keyword.lower() in page_text.lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                page_numbers_with_keywords.append(page_number + 1)
    else:
        for page_number_outer in range(pdf_document.page_count): # This will also pick up the catalog format (often used for photographs)
            page_text = pages_text_json.get(page_number_outer+1, "")
            if any(all(keyword.lower() in page_text.lower() for keyword in keyword_set) for keyword_set in keywords["Copyright"]):
                # we found one, now we can look for any word rather than all words
                for page_number_inner in range(pdf_document.page_count):
                    page_text = pages_text_json.get(page_number_inner+1, "")
                    if any(sum(keyword.lower() in page_text.lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                        page_numbers_with_keywords.append(page_number_inner + 1)
                break
    

    if os.path.isdir(folder_path):
        # 4. Merge both lists the other way arround to see if we really need the assess_similarity_to_template_single_folder
        # 1 time and similar_images_in_pdf was wrong
        for file in similar_images_in_pdf:
            page_number = int(os.path.basename(file).split("page")[1].split("_")[0])
            if page_number not in page_numbers_with_keywords:
                page_numbers_with_keywords.append(page_number)
                print(f"\033[91mPage {page_number} was found by the template but not by the keywords. Full text is: {pages_text_json.get(page_number, '')} \033[0m")
    
    prompt = f'I am looking for registration certificates and associated artwork. You are given a PDF and return me a list of json with the information about each copyright: [{{"registration number": "VA000xxxxxxx", "certificate page": xx, "artwork page": xx}}, {{...}},...]. I have identified the following pages as potentially containing certificates: {page_numbers_with_keywords}. If the certificate is present but not the artwork, return -1 for the artwork page.'
    prompt_list = [("text", prompt), ("pdf_path", os.path.join(case_directory_1, pdf_file))]
    ai_answer = vertex_genai_multi(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    ai_answer_json = get_json_list(ai_answer)
    
    # Ensure structure exists
    if 'exhibit' not in df.at[index, 'images_status']['copyright_status'] or not isinstance(df.at[index, 'images_status']['copyright_status']['exhibit'], dict):
        df.at[index, 'images_status']['copyright_status']['exhibit'] = {'count': 0, 'steps': []}
        
    current_step_name = os.path.basename(os.path.dirname(pdf_file))
    
    for item in ai_answer_json:
        certificate_page = int(item["certificate page"])
        if certificate_page > 0 and certificate_page <= pdf_document.page_count:
            registration_number = extract_formated_copyright_registration_number(item["registration number"])
            identified_reg_nos.append(registration_number)
            
            source_certificate_path = os.path.join(case_directory_1, pdf_file_no_ext, f"{pdf_file_no_ext}_page{certificate_page}_0.jpg")
            dest_certificate_filename = os.path.splitext(os.path.basename(source_certificate_path))[0] + "_full.webp"
            dest_certificate_path = os.path.join(case_images_directory, dest_certificate_filename)
            img = cv2.imread(source_certificate_path)
            if img.shape[1] > img.shape[0]:  # if width > height
                img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
            cv2.imwrite(dest_certificate_path, img, [cv2.IMWRITE_WEBP_QUALITY, 80])
            
            artwork_page = int(item["artwork page"])
            if artwork_page == -1:
                copyright_image_filename_webp = os.path.basename(dest_certificate_path).replace("_full.webp", ".webp")
                # Use the certificate as the image
                shutil.copy(dest_certificate_path, os.path.join(case_images_directory, copyright_image_filename_webp))
                ip_manager.add_copyright_to_dataframe({"registration_number": registration_number, "image_found": 0, "certificate_found": 1}, location_id)
            elif artwork_page > 0 and artwork_page <= pdf_document.page_count:
                copyright_images_folder = os.path.join(folder_path, 'copyright')
                os.makedirs(copyright_images_folder, exist_ok=True)
                artwork_page_image_file = os.path.join(case_directory_1, pdf_file_no_ext, f"{pdf_file_no_ext}_page{artwork_page}_0.jpg")
                extracted_image_paths = extract_images_from_a_pdf_page(pdf_file, pdf_document, artwork_page-1, copyright_images_folder, 5)
                if len(extracted_image_paths) == 0:
                    artwork_image_file = artwork_page_image_file
                else:
                    if len(extracted_image_paths) > 1:
                        log_message(f"        Found {len(extracted_image_paths)} images on page {artwork_page} for {item['registration number']}. Not sure which one to use => asking LLM.", level="WARNING")
                        prompt = 'I am looking for a copyrighted artwork. Which of these images has the artwork? You answer with a json with a single artwork: {"artwork": "image_name"}'
                        prompt_list = [("text", prompt)]
                        for i, image_file in enumerate(extracted_image_paths):
                            prompt_list.append(("text", f"\n\nImage_{i+1}"))
                            prompt_list.append(("image_path", os.path.join(copyright_images_folder, image_file)))
                        ai_answer = vertex_genai_multi(prompt_list, model_name=Constants.SMART_MODEL_FREE)
                        ai_answer_json = get_json(ai_answer)
                        if ai_answer_json and "artwork" in ai_answer_json:
                            artwork_image_tag = ai_answer_json["artwork"]
                            artwork_image_nb = int(re.sub("[^0-9]", "", artwork_image_tag))
                            if (artwork_image_nb-1) >=0 and (artwork_image_nb-1) < len(extracted_image_paths):
                                artwork_image_file = extracted_image_paths[artwork_image_nb - 1]
                            else:
                                log_message(f"        🔥 LLM returned invalid image number {artwork_image_tag} for {registration_number} while the possible values are {list(range(1, len(extracted_image_paths)+1))}.", level="WARNING")
                                continue
                    else:
                        artwork_image_file = extracted_image_paths[0]

                # The image: Remove all the white space around the image
                img = cv2.imread(artwork_image_file)
                cropped_image, (x_offset, y_offset) = crop_white_space(img)
                copyright_image_filename_webp = f"{os.path.splitext(os.path.basename(artwork_image_file))[0]}.webp"
                cv2.imwrite(os.path.join(case_images_directory, copyright_image_filename_webp), cropped_image, [cv2.IMWRITE_WEBP_QUALITY, 80])
                ip_manager.record_finding('copyright', location_id, found_reg_nos=[registration_number])
                ip_manager.add_copyright_to_dataframe({"registration_number": registration_number, "image_found": 1, "image_source": "exhibit"}, location_id)
                identified_reg_nos_with_artwork.append(registration_number)
                df.at[index, 'images_status']['copyright_status']['exhibit']['count'] += 1
                if current_step_name not in df.at[index, 'images_status']['copyright_status']['exhibit'].get('steps', []):
                    df.at[index, 'images_status']['copyright_status']['exhibit'].setdefault('steps', []).append(current_step_name)

            df.at[index, 'images']['copyrights'][copyright_image_filename_webp] = {'full_filename': [os.path.basename(dest_certificate_path)], 'reg_no': [registration_number]}
                    
    
    if identified_reg_nos_with_artwork:
        log_message(f"     ✅ Found Copyright Exhibit in {pdf_file} (Reg Nos with artwork: {identified_reg_nos_with_artwork}, Reg Nos without artwork: {set(identified_reg_nos)-set(identified_reg_nos_with_artwork)})")
    else:
        log_message(f"     ⚠️ Found Copyright Exhibit in {pdf_file} (Reg Nos: {identified_reg_nos}) but Artwork found.")
    
    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "PDFFile": os.path.basename(pdf_file),
            "Certainty": certainty,
            "NumPages": pdf_document.page_count,
            "LocationID": location_id
        },
        output={
            "Registration Numbers Found": identified_reg_nos,
            "Registration Numbers with Artwork Found": identified_reg_nos_with_artwork
        }
    )
    return identified_reg_nos


@observe(capture_input=False, capture_output=False)
def process_copyright_main_pdf(df, index, case_images_directory, pdf_path, reg_nos: List[str], ip_manager: IPTrackingManager):
    """
    Processes a PDF to find and extract copyright images for the given registration numbers.
    Updates the DataFrame and IPTrackingManager with found images.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        pdf_path: Path to the PDF file to process.
        reg_nos: List of registration numbers to search for in the PDF.
        ip_manager: Instance of IPTrackingManager.
    
    Returns:
        List[str]: A list of registration numbers for which images were NOT found in this PDF.
    """
    if not pdf_path or not os.path.exists(pdf_path):
        log_message(f"PDF path is invalid or file does not exist: {pdf_path}", level="ERROR")
        return reg_nos # Return all reg_nos as not found if PDF is invalid

    log_message(f"Processing PDF {os.path.basename(pdf_path)} for {len(reg_nos)} copyright registration numbers.", level="INFO")
    
    # Initialize images and images_status if they don't exist or are not dicts
    if 'images' not in df.columns or not isinstance(df.at[index, 'images'], dict):
        df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    if 'copyrights' not in df.at[index, 'images'] or not isinstance(df.at[index, 'images']['copyrights'], dict):
        df.at[index, 'images']['copyrights'] = {}

    if 'images_status' not in df.columns or not isinstance(df.at[index, 'images_status'], dict):
        # A more robust initialization might be needed if this is the first time
        df.at[index, 'images_status'] = {'copyright_status': {'byregno': {'ai_reg_nos': [], 'count': 0}}}
    if 'copyright_status' not in df.at[index, 'images_status'] or not isinstance(df.at[index, 'images_status']['copyright_status'], dict):
        df.at[index, 'images_status']['copyright_status'] = {'byregno': {'ai_reg_nos': [], 'count': 0}}
    if 'byregno' not in df.at[index, 'images_status']['copyright_status'] or not isinstance(df.at[index, 'images_status']['copyright_status']['byregno'], dict):
        df.at[index, 'images_status']['copyright_status']['byregno'] = {'ai_reg_nos': [], 'count': 0}
    if 'ai_reg_nos' not in df.at[index, 'images_status']['copyright_status']['byregno']:
        df.at[index, 'images_status']['copyright_status']['byregno']['ai_reg_nos'] = []
    
    current_ai_nos = df.at[index, 'images_status']['copyright_status']['byregno'].get('ai_reg_nos', [])
    df.at[index, 'images_status']['copyright_status']['byregno']['ai_reg_nos'] = list(set(current_ai_nos + reg_nos))

    # Filter for valid "VA" numbers and those not already found
    valid_cr_nos = [str(reg_no) for reg_no in reg_nos if isinstance(reg_no, str) and reg_no.strip().upper().startswith("VA")]
    already_found_copyrights_details = df.at[index, 'images'].get("copyrights", {})
    already_found_reg_nos = set()
    if isinstance(already_found_copyrights_details, dict):
        for item_details in already_found_copyrights_details.values():
            if isinstance(item_details, dict) and 'reg_no' in item_details:
                reg_no_val = item_details['reg_no']
                if isinstance(reg_no_val, list):
                    already_found_reg_nos.update(reg_no_val)
                else:
                    already_found_reg_nos.add(str(reg_no_val))

    reg_nos_to_process_in_pdf = [reg_no for reg_no in valid_cr_nos if reg_no not in already_found_reg_nos]

    if not reg_nos_to_process_in_pdf:
        log_message(f"No new valid copyright registration numbers to process in {os.path.basename(pdf_path)}.", level="INFO")
        return []

    processed_count_in_pdf = 0
    reg_nos_not_found_in_this_pdf = []
    
    try:
        # Call to OCRProcessor.process_pdf: it returns (full_text, pages_text_json, page_ocr_data, page_images). We only need page_images for get_image_parts here.
        full_text, page_text, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_path)
    except Exception as e:
        log_message(f"Error during OCR processing of {pdf_path}: {e}", level="ERROR")
        return reg_nos_to_process_in_pdf

    prompt = """
    You are an expert in copyright registration numbers.
    You are given a list of copyright registration numbers and a document related to the court filings. For each number, you need to find the corresponding copyrighted picture in the document and return the page number of the picture.
    You return your answer in a JSON format with the following keys: {
    """
                                                                      
    for reg_no in reg_nos_to_process_in_pdf:
        prompt += f'"{reg_no}": page_number, '
    prompt += "}. Do not include REDACTED registration numbers in your answer. If a registration number has no associated copyrighted picture (we are looking for a picture of the artwork!!!), do not include the registration number in the JSON! Do not provide the page of the registration number if the picture of the artwork is not present."

    prompt_list = [("text", prompt), ("text", f"\n\nThis is pdf file: "), ("pdf_path", pdf_path)]

    ai_answer = vertex_genai_multi(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    ai_answer_json = get_json(ai_answer)

    reg_nos_llm_responded_for = set(ai_answer_json.keys()) if isinstance(ai_answer_json, dict) else set()

    for reg_no in reg_nos_to_process_in_pdf:
        if reg_no not in reg_nos_llm_responded_for:
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue

        item = ai_answer_json.get(reg_no)
        try:
            page_number = int(item)
            if page_number not in page_images:
                log_message(f"LLM returned invalid page number {page_number} for {reg_no} in {os.path.basename(pdf_path)}", level="WARNING")
                reg_nos_not_found_in_this_pdf.append(reg_no)
                continue
            image_page_content = page_images[page_number]
        except (ValueError, TypeError, KeyError):
            log_message(f"Error parsing LLM result or finding page for {reg_no} (item: {item}) in {os.path.basename(pdf_path)}", level="WARNING")
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue
        
        gcv_prompt = f"Identify a single (only 1!) bounding box for the Copyrighted Picture with registration number {reg_no}."
        parts_details = get_image_parts(gcv_prompt, image_page_content, image_format='webp')
        
        if not parts_details:
            log_message(f"🔥 No bounding box found by GCV for {reg_no} on page {page_number} in {os.path.basename(pdf_path)}", level="WARNING")
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue
        elif len(parts_details) > 1:
            log_message(f"🔥 More than 1 bounding box found by GCV for {reg_no} on page {page_number} in {os.path.basename(pdf_path)}. Taking the first one.", level="WARNING")
        
        copyright_image_path_from_gcv = parts_details[0]["path"]
        copyright_image_filename = os.path.basename(copyright_image_path_from_gcv)
        
        os.makedirs(case_images_directory, exist_ok=True)
        
        final_image_path = os.path.join(case_images_directory, copyright_image_filename)
        shutil.copy(copyright_image_path_from_gcv, final_image_path)
        
        copyright_image_full_filename = os.path.splitext(copyright_image_filename)[0] + "_full.webp"
        final_full_image_path = os.path.join(case_images_directory, copyright_image_full_filename)
        shutil.copy(final_image_path, final_full_image_path)
        
        if 'copyrights' not in df.at[index, 'images'] or not isinstance(df.at[index, 'images']['copyrights'], dict):
            df.at[index, 'images']['copyrights'] = {}
        df.at[index, 'images']['copyrights'][copyright_image_filename] = {'reg_no': [reg_no],'full_filename': [copyright_image_full_filename]}
        processed_count_in_pdf += 1

        location_id = f"pdf-{os.path.basename(pdf_path)}-regno-{reg_no}"
        ip_manager.record_finding('copyright', location_id, found_reg_nos=[reg_no])
        log_message(f"Successfully processed and saved image for {reg_no} from {os.path.basename(pdf_path)} page {page_number}.", level="INFO")

    if 'count' not in df.at[index, 'images_status']['copyright_status']['byregno']:
        df.at[index, 'images_status']['copyright_status']['byregno']['count'] = 0
    df.at[index, 'images_status']['copyright_status']['byregno']['count'] += processed_count_in_pdf
    
    langfuse.get_client().update_current_span(
        input={"NumRegNos": {len(reg_nos_to_process_in_pdf)}, "PDFPath": {os.path.basename(pdf_path)}},
        output={"LLMJsonForPages": {ai_answer_json if 'ai_answer_json' in locals() and ai_answer_json is not None else 'None/Error'}, "ImagesFoundViaLLMInPDF": {processed_count_in_pdf}, "RegNosNotProcessedFromPDF": {len(reg_nos_not_found_in_this_pdf)}, "CopyrightGoalMetAfter": {ip_manager.is_goal_met('copyright')}}
    )
    return reg_nos_not_found_in_this_pdf