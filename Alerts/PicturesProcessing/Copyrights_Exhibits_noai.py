import os
import sys
from typing import Optional
sys.path.append(os.getcwd())
import cv2
import numpy as np
import os
from PIL import Image
import shutil
import re
from logdata import log_message
from Alerts.PicturesProcessing.ProcessPicturesShared import crop_white_space, keywords, assess_similarity_to_template_single_folder
from Alerts.PicturesProcessing.ExtractPictures import  extract_images_from_a_pdf_page
from AI.GC_VertexAI import vertex_genai_multi
from AI.GCV_GetImageParts import get_image_parts
from AI.LLM_shared import get_json, get_json_list
from langfuse import observe
import langfuse
from IP.Copyrights.Copyright_USCO import extract_formated_copyright_registration_number
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor # Added import for OCRProcessor
from typing import List
import Common.Constants as Constants

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)


@observe(capture_input=False, capture_output=False)
def process_copyright_exhibit(df, index, ref_hash, size, case_images_directory, case_directory_1, pdf_document, pdf_file, pages_text_json, certainty, ip_manager: IPTrackingManager, location_id: str):
    """
    Processes a potential copyright exhibit PDF to find copyright certificates and associated images.
    Updates IPTrackingManager based on findings.

    Returns:
        tuple: (bool, bool, list[str])
            - bool: True if copyright exhibit text (registration numbers) was found.
            - bool: True if distinct copyright artwork images were successfully linked to any of those registration numbers.
            - list[str]: A list of unique registration numbers textually identified in the exhibit.
    """
    log_message(f"        - Extracting images from the pdf for exhibit processing at {location_id}.")
    pdf_file_no_ext = os.path.splitext(pdf_file)[0]
    similar_images_in_pdf = [] # List of paths to certificate images
    textually_identified_reg_nos_in_exhibit = []
    
    # 1. Image template approach
    folder_path = os.path.join(case_directory_1, pdf_file_no_ext)
    log_message(f"        - Assessing similarity of images to copyright template")
    if os.path.isdir(folder_path):
        similar_images_in_pdf, differences, total_similar_images_folder = assess_similarity_to_template_single_folder(folder_path, ref_hash, size, 20)

    # 2. Keywords approach
    page_numbers_with_keywords = []
    if similar_images_in_pdf or certainty=="high" or ip_manager.is_goal_relevant('copyright'): # if it is the right document, we be easy on keywords
        for page_number in range(pdf_document.page_count):
            if any(sum(keyword.lower() in pages_text_json[page_number+1].lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                page_numbers_with_keywords.append(page_number + 1)
    else:
        for page_number in range(pdf_document.page_count): # This will also pick up the catalog format (often used for photographs)
            if any(all(keyword.lower() in pages_text_json[page_number+1].lower() for keyword in keyword_set) for keyword_set in keywords["Copyright"]):
                # we found one, now we can look for any word rather than all words
                for page_number in range(pdf_document.page_count):
                    if any(sum(keyword.lower() in pages_text_json[page_number+1].lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                        page_numbers_with_keywords.append(page_number + 1)
                break
    

    if os.path.isdir(folder_path):
        # 4. Merge both lists
        for page_number in page_numbers_with_keywords:
            image_on_page = [file for file in os.listdir(os.path.join(case_directory_1, pdf_file_no_ext)) if file.startswith(f"{pdf_file_no_ext}_page{page_number}_0")]
            if os.path.join(case_directory_1, pdf_file_no_ext, image_on_page[0]) not in similar_images_in_pdf:
                similar_images_in_pdf.append(os.path.join(case_directory_1, pdf_file_no_ext, image_on_page[0]))

        # 4. Merge both lists the other way arround to see if we really need the assess_similarity_to_template_single_folder
        # 1 time and similar_images_in_pdf was wrong
        for file in similar_images_in_pdf:
            page_number = int(os.path.basename(file).split("page")[1].split("_")[0])
            if page_number not in page_numbers_with_keywords:
                page_numbers_with_keywords.append(page_number)
                print(f"\033[91mPage {page_number} was found by the template but not by the keywords. Full text is: {pages_text_json[page_number]} \033[0m")             
    
    # 5. Process the list
    if similar_images_in_pdf:
        log_message(f"        - Extracting copyright information from IP pages: {len(similar_images_in_pdf)} IP pages")
        page_numbers = [int(file.split("page")[1].split("_")[0]) for file in similar_images_in_pdf]
        sorted_page_numbers = sorted(list(set(page_numbers)))
        image_pages_processed_with_artwork = 0 # Counter for distinct artwork images linked

        copyright_images_folder = os.path.join(folder_path, 'copyright')
        if os.path.exists(copyright_images_folder): # Clear previous run for this PDF
            shutil.rmtree(copyright_images_folder)
        os.makedirs(copyright_images_folder, exist_ok=True)
        
        for source_certificate_path in similar_images_in_pdf: # similar_images_in_pdf contains paths to certificate images
            dest_certificate_filename = os.path.splitext(os.path.basename(source_certificate_path))[0] + "_full.webp"
            dest_certificate_path = os.path.join(case_images_directory, dest_certificate_filename)

            copyright_page_number = int(dest_certificate_filename.split("page")[1].split("_")[0])
            registration_number = extract_formated_copyright_registration_number(pages_text_json[copyright_page_number], source_certificate_path)
            
            if registration_number:
                if registration_number not in textually_identified_reg_nos_in_exhibit:
                    textually_identified_reg_nos_in_exhibit.append(registration_number)
                
                # Attempt to find and save artwork image for this registration_number
                artwork_linked_for_this_reg_no = False
                next_copyright_page_number = sorted_page_numbers[sorted_page_numbers.index(copyright_page_number) + 1] if sorted_page_numbers.index(copyright_page_number) + 1 < len(sorted_page_numbers) else (pdf_document.page_count + 1)
                
                if next_copyright_page_number - copyright_page_number == 1:
                    # log_message(f"        No artwork page between certificate page {copyright_page_number} and next certificate page {next_copyright_page_number} for {registration_number}.")
                    save_copyright_without_image(df, index, source_certificate_path, case_images_directory, dest_certificate_path, registration_number)
                else:
                    # Extract images from potential artwork pages
                    for page_num_to_extract in range(copyright_page_number + 1, next_copyright_page_number):
                        extract_images_from_a_pdf_page(pdf_file, pdf_document, page_num_to_extract - 1, copyright_images_folder, 5)
                    
                    potential_artwork_files = [f for f in os.listdir(copyright_images_folder) if int(f.split("page")[1].split("_")[0]) in range(copyright_page_number + 1, next_copyright_page_number)]
                    
                    if potential_artwork_files:
                        if len(potential_artwork_files) == 1:
                            artwork_image_file = potential_artwork_files[0]
                        else:
                            prompt = 'I am looking for a copyrighted artwork. Which of these images has the artwork? You answer with a json with a single artwork: {"artwork": "image_name"}'
                            prompt_list = [("text", prompt)]
                            for i, image_file in enumerate(potential_artwork_files):
                                prompt_list.append(("text", f"\n\nImage_{i+1}"))
                                prompt_list.append(("image_path", os.path.join(copyright_images_folder, image_file)))
                            ai_answer = vertex_genai_multi(prompt_list, model_name=Constants.SMART_MODEL_FREE)
                            ai_answer_json = get_json(ai_answer)
                            if ai_answer_json and "artwork" in ai_answer_json:
                                artwork_image_tag = ai_answer_json["artwork"]
                                artwork_image_nb = int(re.sub("[^0-9]", "", artwork_image_tag))
                                if (artwork_image_nb-1) >=0 and (artwork_image_nb-1) < len(potential_artwork_files):
                                    artwork_image_file = potential_artwork_files[artwork_image_nb - 1]
                                else:
                                    log_message(f"        🔥 LLM returned invalid image number {artwork_image_tag} for {registration_number} while the possible values are {list(range(1, len(potential_artwork_files)+1))}.", level="WARNING")
                                    continue
                        save_copyright_with_image(df, index, source_certificate_path, copyright_images_folder, case_images_directory, artwork_image_file, dest_certificate_path, registration_number)
                        artwork_linked_for_this_reg_no = True

                    
                    # potential_artwork_pages = [
                    #     pg_num for pg_num in range(copyright_page_number + 1, next_copyright_page_number)
                    #     if any(f"{pdf_file_no_ext}_page{pg_num}" in fname for fname in os.listdir(copyright_images_folder))
                    # ]
                
                    # best_artwork_page_found = None
                    # for image_page_candidate in potential_artwork_pages:
                    #     # Check if page is unlikely to be an artwork (too much text, specific keywords)
                    #     if not any(keyword.lower() in pages_text_json[image_page_candidate].lower() for keyword in ["author", "copyright claimant", "certification", "correspondence", "name", "date", "registration", "service", "request", "library of congress", "certificate of registration", "certificate issued", "under the seal", "title 17"]):
                    #         best_artwork_page_found = image_page_candidate # Take the first suitable one for simplicity
                    #         break
                    
                    # if best_artwork_page_found:
                    #     files_on_best_page = [f for f in os.listdir(copyright_images_folder) if int(f.split("page")[1].split("_")[0]) == best_artwork_page_found]
                    #     if len(files_on_best_page) == 1:
                    #         save_copyright_with_image(df, index, source_certificate_path, copyright_images_folder, case_images_directory, files_on_best_page[0], dest_certificate_path, registration_number)
                    #         artwork_linked_for_this_reg_no = True
                    #     elif len(files_on_best_page) > 1:
                    #         best_image_filename = get_copyright_image_best_scores(copyright_images_folder, files_on_best_page)
                    #         save_copyright_with_image(df, index, source_certificate_path, copyright_images_folder, case_images_directory, best_image_filename, dest_certificate_path, registration_number)
                    #         artwork_linked_for_this_reg_no = True
                    #     else: # No files on the "best" page, should not happen if best_artwork_page_found is set
                    #         save_copyright_without_image(df, index, source_certificate_path, case_images_directory, dest_certificate_path, registration_number)
                    else: # No suitable artwork page found
                        save_copyright_without_image(df, index, source_certificate_path, case_images_directory, dest_certificate_path, registration_number)

                if artwork_linked_for_this_reg_no:
                    image_pages_processed_with_artwork += 1
            else: # registration_number is None
                continue # Skip to next certificate image

        # Overwrite logic if less than 50% of certificates have distinct artwork images
        if image_pages_processed_with_artwork < 0.5 * len(similar_images_in_pdf) and similar_images_in_pdf:
            log_message(f"        Less than 50% of certificates have distinct artwork images ({image_pages_processed_with_artwork}/{len(similar_images_in_pdf)}). Using certificate images as placeholders.")
            image_pages_processed_with_artwork = 0 # Reset counter as they are no longer distinct artworks
            # This loop might re-process. We need to be careful.
            # It iterates over df.at[index, 'images']['copyrights'] which might contain entries from other PDFs/steps.
            # We should only adjust entries related to *this* exhibit's textually_identified_reg_nos_in_exhibit.
            
            # Create a temporary list of items to update to avoid issues with dict size change during iteration
            items_to_update_for_cert_copy = []
            if isinstance(df.at[index, 'images'], dict) and isinstance(df.at[index, 'images'].get('copyrights'), dict):
                for artwork_filename, details in df.at[index, 'images']['copyrights'].items():
                    reg_nos_in_detail = details.get('reg_no', [])
                    if not isinstance(reg_nos_in_detail, list): reg_nos_in_detail = [reg_nos_in_detail]
                    
                    # Check if any reg_no in this df entry was textually ID'd in the current exhibit
                    if any(rn_detail in textually_identified_reg_nos_in_exhibit for rn_detail in reg_nos_in_detail):
                        # And if the artwork_filename suggests it's from the current PDF
                        if artwork_filename.startswith(pdf_file_no_ext):
                             items_to_update_for_cert_copy.append(artwork_filename)
            
            for item_key in items_to_update_for_cert_copy:
                cert_full_path = os.path.join(case_images_directory, df.at[index, 'images']['copyrights'][item_key]["full_filename"][0])
                artwork_path_to_overwrite = os.path.join(case_images_directory, item_key)
                if os.path.exists(cert_full_path):
                    shutil.copy(cert_full_path, artwork_path_to_overwrite)
                else:
                    log_message(f"        Warning: Certificate source {cert_full_path} not found for overwriting {item_key}", level="WARNING")


        # Update images_status field with exhibit metadata if distinct artworks were found
        if image_pages_processed_with_artwork > 0:
            # Ensure structure exists
            if 'exhibit' not in df.at[index, 'images_status']['copyright_status']:
                df.at[index, 'images_status']['copyright_status']['exhibit'] = {'count': 0, 'steps': []}
            elif not isinstance(df.at[index, 'images_status']['copyright_status']['exhibit'], dict):
                 df.at[index, 'images_status']['copyright_status']['exhibit'] = {'count': 0, 'steps': []}

            df.at[index, 'images_status']['copyright_status']['exhibit']['count'] += image_pages_processed_with_artwork
            current_step_name = os.path.basename(os.path.dirname(pdf_file)) # e.g., "step_1" or pdf_file_no_ext if no step subfolder
            if current_step_name not in df.at[index, 'images_status']['copyright_status']['exhibit'].get('steps', []):
                 df.at[index, 'images_status']['copyright_status']['exhibit'].setdefault('steps', []).append(current_step_name)


    # --- Final IPTrackingManager Update ---
    final_reg_nos_with_distinct_artwork = set()
    if isinstance(df.at[index, 'images'], dict) and isinstance(df.at[index, 'images'].get('copyrights'), dict):
        for artwork_filename, details in df.at[index, 'images']['copyrights'].items():
            # We only care about reg_nos identified textually in *this* exhibit
            reg_nos_for_this_artwork_entry = details.get('reg_no', [])
            if not isinstance(reg_nos_for_this_artwork_entry, list): reg_nos_for_this_artwork_entry = [reg_nos_for_this_artwork_entry]

            for rn_candidate in reg_nos_for_this_artwork_entry:
                if rn_candidate in textually_identified_reg_nos_in_exhibit:
                    # Check if it's a distinct artwork
                    cert_full_filename = details.get('full_filename', [""])[0]
                    # If artwork_filename is the same base as cert_full_filename (after stripping _full.webp), it's a cert copy
                    if not cert_full_filename.replace("_full.webp", ".webp") == artwork_filename:
                        final_reg_nos_with_distinct_artwork.add(rn_candidate)

    for reg_no in textually_identified_reg_nos_in_exhibit:
        if reg_no in final_reg_nos_with_distinct_artwork:
            ip_manager.add_copyright_to_dataframe({"registration_number": reg_no, "image_found": 1, "image_source": "exhibit"}, location_id)
            ip_manager.record_finding('copyright', location_id, found_reg_nos=[reg_no])
        else:
            # Certificate found, but artwork not in this exhibit. Mark for later search.
            ip_manager.add_copyright_to_dataframe({"registration_number": reg_no, "image_found": 0}, location_id)
            
    _is_copyright_exhibit_text_found = bool(textually_identified_reg_nos_in_exhibit)
    _has_distinct_artwork_images_linked = bool(final_reg_nos_with_distinct_artwork)
    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "PDFFile": os.path.basename(pdf_file),
            "Certainty": certainty,
            "NumPages": pdf_document.page_count,
            "LocationID": location_id
        },
        output={
            "ExhibitTextFound": _is_copyright_exhibit_text_found,
            "DistinctArtworkImagesLinked": _has_distinct_artwork_images_linked,
            "TextualRegNosCount": len(textually_identified_reg_nos_in_exhibit)
        }
    )
    return _is_copyright_exhibit_text_found, _has_distinct_artwork_images_linked, textually_identified_reg_nos_in_exhibit

def save_copyright_with_image(df, index, source_certificate_path, copyright_images_folder, case_images_directory, copyright_image_filename, dest_certificate_path, registration_number):
    # 1. The Certificate: Check if width is more than length, and if so rotate by 90 degrees
    img = cv2.imread(source_certificate_path)
    if img.shape[1] > img.shape[0]:  # if width > height
        img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
    cv2.imwrite(dest_certificate_path, img, [cv2.IMWRITE_WEBP_QUALITY, 80])

    # 2. The image: Remove all the white space around the image
    img = cv2.imread(os.path.join(copyright_images_folder, copyright_image_filename))
    cropped_image, (x_offset, y_offset) = crop_white_space(img)
    copyright_image_filename_webp = f"{os.path.splitext(copyright_image_filename)[0]}.webp"
    params = [cv2.IMWRITE_WEBP_QUALITY, 80]
    cv2.imwrite(os.path.join(case_images_directory, copyright_image_filename_webp), cropped_image, params)
    # print(f"Copyright image {os.path.basename(file_path)} and next page {next_page_filename}")
    # Ensure reg_no is always a list
    df.at[index, 'images']['copyrights'][copyright_image_filename_webp] = {'full_filename': [os.path.basename(dest_certificate_path)], 'reg_no': [registration_number]}


def save_copyright_without_image(df, index, source_certificate_path, case_images_directory, dest_certificate_path, registration_number):
    # 1. The Certificate: Check if width is more than length, and if so rotate by 90 degrees
    img = cv2.imread(source_certificate_path)
    if img.shape[1] > img.shape[0]:
        img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
    cv2.imwrite(dest_certificate_path, img, [cv2.IMWRITE_WEBP_QUALITY, 80])
    
    copyright_image_filename_webp = os.path.basename(dest_certificate_path).replace("_full.webp", ".webp")
    shutil.copy(dest_certificate_path, os.path.join(case_images_directory, copyright_image_filename_webp))
    # img = cv2.imread(full_filepath)
    # cv2.imwrite(os.path.join(case_images_directory, copyrighted_image_filename), img, [cv2.IMWRITE_WEBP_QUALITY, 80])

    # print(f"Copyright image {os.path.basename(source_certificate_path)} without image")
    # Ensure reg_no is always a list
    df.at[index, 'images']['copyrights'][copyright_image_filename_webp] = {'full_filename': [os.path.basename(dest_certificate_path)], 'reg_no': [registration_number]}

def get_copyright_image_best_scores(folder_path, files_with_next_page):
    scores = {}
    all_filepaths = [os.path.join(folder_path, f) for f in files_with_next_page]
    
    # Calculate aspect ratios
    aspect_ratios = {}
    for filepath in all_filepaths:
        with Image.open(filepath) as img:
            width, height = img.size
            aspect_ratios[filepath] = width / height
    
    # Calculate file sizes
    file_sizes = {filepath: os.path.getsize(filepath) / (1024 * 1024) for filepath in all_filepaths}
    
    # Calculate colorfulness
    saturations = {}
    for filepath in all_filepaths:
        with Image.open(filepath) as img:
            if img.mode == 'RGB':
                img_array = np.array(img)
                saturations[filepath] = np.sum(np.std(img_array, axis=2))
            else:
                saturations[filepath] = 0
    
    for filepath in all_filepaths:
        points = 0
        points += min(max(0, 1 - max(0, aspect_ratios[filepath] - 1) / 10), aspect_ratios[filepath]) # Aspect ratio point close to 1: 1. For x = 1, the output is 1, For x = 0, the output is 0, For x = 10, the output is 0
        points += file_sizes[filepath] / max(file_sizes.values()) # File size point
        points += saturations[filepath] / max(1, max(saturations.values())) # Colorfulness point
        scores[os.path.basename(filepath)] = points

    best_image = max(scores, key=scores.get)
    
    return best_image


@observe(capture_input=False, capture_output=False)
def process_copyright_main_pdf(df, index, case_images_directory, pdf_path, reg_nos: List[str], ip_manager: IPTrackingManager):
    """
    Processes a PDF to find and extract copyright images for the given registration numbers.
    Updates the DataFrame and IPTrackingManager with found images.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        pdf_path: Path to the PDF file to process.
        reg_nos: List of registration numbers to search for in the PDF.
        ip_manager: Instance of IPTrackingManager.
    
    Returns:
        List[str]: A list of registration numbers for which images were NOT found in this PDF.
    """
    if not pdf_path or not os.path.exists(pdf_path):
        log_message(f"PDF path is invalid or file does not exist: {pdf_path}", level="ERROR")
        return reg_nos # Return all reg_nos as not found if PDF is invalid

    log_message(f"Processing PDF {os.path.basename(pdf_path)} for {len(reg_nos)} copyright registration numbers.", level="INFO")
    
    # Initialize images and images_status if they don't exist or are not dicts
    if 'images' not in df.columns or not isinstance(df.at[index, 'images'], dict):
        df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    if 'copyrights' not in df.at[index, 'images'] or not isinstance(df.at[index, 'images']['copyrights'], dict):
        df.at[index, 'images']['copyrights'] = {}

    if 'images_status' not in df.columns or not isinstance(df.at[index, 'images_status'], dict):
        # A more robust initialization might be needed if this is the first time
        df.at[index, 'images_status'] = {'copyright_status': {'byregno': {'ai_reg_nos': [], 'count': 0}}}
    if 'copyright_status' not in df.at[index, 'images_status'] or not isinstance(df.at[index, 'images_status']['copyright_status'], dict):
        df.at[index, 'images_status']['copyright_status'] = {'byregno': {'ai_reg_nos': [], 'count': 0}}
    if 'byregno' not in df.at[index, 'images_status']['copyright_status'] or not isinstance(df.at[index, 'images_status']['copyright_status']['byregno'], dict):
        df.at[index, 'images_status']['copyright_status']['byregno'] = {'ai_reg_nos': [], 'count': 0}
    if 'ai_reg_nos' not in df.at[index, 'images_status']['copyright_status']['byregno']:
        df.at[index, 'images_status']['copyright_status']['byregno']['ai_reg_nos'] = []
    
    current_ai_nos = df.at[index, 'images_status']['copyright_status']['byregno'].get('ai_reg_nos', [])
    df.at[index, 'images_status']['copyright_status']['byregno']['ai_reg_nos'] = list(set(current_ai_nos + reg_nos))

    # Filter for valid "VA" numbers and those not already found
    valid_cr_nos = [str(reg_no) for reg_no in reg_nos if isinstance(reg_no, str) and reg_no.strip().upper().startswith("VA")]
    already_found_copyrights_details = df.at[index, 'images'].get("copyrights", {})
    already_found_reg_nos = set()
    if isinstance(already_found_copyrights_details, dict):
        for item_details in already_found_copyrights_details.values():
            if isinstance(item_details, dict) and 'reg_no' in item_details:
                reg_no_val = item_details['reg_no']
                if isinstance(reg_no_val, list):
                    already_found_reg_nos.update(reg_no_val)
                else:
                    already_found_reg_nos.add(str(reg_no_val))

    reg_nos_to_process_in_pdf = [reg_no for reg_no in valid_cr_nos if reg_no not in already_found_reg_nos]

    if not reg_nos_to_process_in_pdf:
        log_message(f"No new valid copyright registration numbers to process in {os.path.basename(pdf_path)}.", level="INFO")
        return []

    processed_count_in_pdf = 0
    reg_nos_not_found_in_this_pdf = []
    
    try:
        # Call to OCRProcessor.process_pdf: it returns (full_text, pages_text_json, page_ocr_data, page_images). We only need page_images for get_image_parts here.
        full_text, page_text, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_path)
    except Exception as e:
        log_message(f"Error during OCR processing of {pdf_path}: {e}", level="ERROR")
        return reg_nos_to_process_in_pdf

    prompt = """
    You are an expert in copyright registration numbers.
    You are given a list of copyright registration numbers and a document related to the court filings. For each number, you need to find the corresponding copyrighted picture in the document and return the page number of the picture.
    You return your answer in a JSON format with the following keys: {
    """
                                                                      
    for reg_no in reg_nos_to_process_in_pdf:
        prompt += f'"{reg_no}": page_number, '
    prompt += "}. Do not include REDACTED registration numbers in your answer. If a registration number has no associated copyrighted picture (we are looking for a picture of the artwork!!!), do not include the registration number in the JSON! Do not provide the page of the registration number if the picture of the artwork is not present."

    prompt_list = [("text", prompt), ("text", f"\n\nThis is pdf file: "), ("pdf_path", pdf_path)]

    ai_answer = vertex_genai_multi(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    ai_answer_json = get_json(ai_answer)

    reg_nos_llm_responded_for = set(ai_answer_json.keys()) if isinstance(ai_answer_json, dict) else set()

    for reg_no in reg_nos_to_process_in_pdf:
        if reg_no not in reg_nos_llm_responded_for:
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue

        item = ai_answer_json.get(reg_no)
        try:
            page_number = int(item)
            if page_number not in page_images:
                log_message(f"LLM returned invalid page number {page_number} for {reg_no} in {os.path.basename(pdf_path)}", level="WARNING")
                reg_nos_not_found_in_this_pdf.append(reg_no)
                continue
            image_page_content = page_images[page_number]
        except (ValueError, TypeError, KeyError):
            log_message(f"Error parsing LLM result or finding page for {reg_no} (item: {item}) in {os.path.basename(pdf_path)}", level="WARNING")
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue
        
        gcv_prompt = f"Identify a single (only 1!) bounding box for the Copyrighted Picture with registration number {reg_no}."
        parts_details = get_image_parts(gcv_prompt, image_page_content, image_format='webp')
        
        if not parts_details:
            log_message(f"🔥 No bounding box found by GCV for {reg_no} on page {page_number} in {os.path.basename(pdf_path)}", level="WARNING")
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue
        elif len(parts_details) > 1:
            log_message(f"🔥 More than 1 bounding box found by GCV for {reg_no} on page {page_number} in {os.path.basename(pdf_path)}. Taking the first one.", level="WARNING")
        
        copyright_image_path_from_gcv = parts_details[0]["path"]
        copyright_image_filename = os.path.basename(copyright_image_path_from_gcv)
        
        os.makedirs(case_images_directory, exist_ok=True)
        
        final_image_path = os.path.join(case_images_directory, copyright_image_filename)
        shutil.copy(copyright_image_path_from_gcv, final_image_path)
        
        copyright_image_full_filename = os.path.splitext(copyright_image_filename)[0] + "_full.webp"
        final_full_image_path = os.path.join(case_images_directory, copyright_image_full_filename)
        shutil.copy(final_image_path, final_full_image_path)
        
        if 'copyrights' not in df.at[index, 'images'] or not isinstance(df.at[index, 'images']['copyrights'], dict):
            df.at[index, 'images']['copyrights'] = {}
        df.at[index, 'images']['copyrights'][copyright_image_filename] = {'reg_no': [reg_no],'full_filename': [copyright_image_full_filename]}
        processed_count_in_pdf += 1

        location_id = f"pdf-{os.path.basename(pdf_path)}-regno-{reg_no}"
        ip_manager.record_finding('copyright', location_id, found_reg_nos=[reg_no])
        log_message(f"Successfully processed and saved image for {reg_no} from {os.path.basename(pdf_path)} page {page_number}.", level="INFO")

    if 'count' not in df.at[index, 'images_status']['copyright_status']['byregno']:
        df.at[index, 'images_status']['copyright_status']['byregno']['count'] = 0
    df.at[index, 'images_status']['copyright_status']['byregno']['count'] += processed_count_in_pdf
    
    langfuse.get_client().update_current_span(
        input={"NumRegNos": {len(reg_nos_to_process_in_pdf)}, "PDFPath": {os.path.basename(pdf_path)}},
        output={"LLMJsonForPages": {ai_answer_json if 'ai_answer_json' in locals() and ai_answer_json is not None else 'None/Error'}, "ImagesFoundViaLLMInPDF": {processed_count_in_pdf}, "RegNosNotProcessedFromPDF": {len(reg_nos_not_found_in_this_pdf)}, "CopyrightGoalMetAfter": {ip_manager.is_goal_met('copyright')}}
    )
    return reg_nos_not_found_in_this_pdf