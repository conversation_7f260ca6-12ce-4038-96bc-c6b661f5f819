#!/usr/bin/env python3
"""
Migration script to convert copyright filenames from reg_no based to a standardized format.
Converts existing filenames to {reg_no}_{method}.webp or {reg_no}_{Exhibit}.webp.

This script:
1. Processes all cases with copyright images in the dataframe.
2. Cleans and formats reg_no, or generates a new one if missing.
3. Updates the 'copyrights' table in the PostgreSQL database.
4. Copies files to new names based on the standardized format.
5. Updates dataframe with new filenames and reg_no information.
"""

import os
import sys
sys.path.append(os.getcwd())
import re
import asyncio
import json
import copy
from typing import Dict, Optional, Tuple
import traceback

USER_ANSWERS_CACHE_FILE = os.path.join(os.path.dirname(__file__), "user_answers_cache.json")

sys.path.append(os.getcwd())

import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from IP.Patents_Bulk.patent_db_grant import get_db_connection
from FileManagement.Tencent_COS import get_cos_client, async_copy_file_with_retry
from IP.Copyrights.Copyright_USCO import get_info_from_USCO_using_reg_no

def split_reg(reg_no: str) -> list:
    formated_reg_no = re.sub(r'[^a-zA-Z0-9]', '', reg_no)
    return re.findall(r'([a-zA-Z]+|[0-9]+)', formated_reg_no)

class CopyrightFilenameMigrator:
    def __init__(self, existing_md_copyright_list: set):
        self.db_conn = None
        self.cos_client = None
        self.cos_bucket = None
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
        self.copyright_md_list = existing_md_copyright_list

    async def __aenter__(self):
        self.db_conn = get_db_connection()
        self.cos_client, self.cos_bucket = get_cos_client()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()

    def __enter__(self):
        self.db_conn = get_db_connection()
        self.cos_client, self.cos_bucket = get_cos_client()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()
            

    async def ask_llm_for_numbers(self, local_image_path: str, reg_no: str) -> Tuple[Optional[str], Optional[str]]:
        """Use LLM to identify reg_no and ser_no from certificate image"""
        try:
            prompt = f"""
            Looking at this copyright certificate image, please identify the registration number.
            
            
            If you cannot find either number, respond with "NOT_FOUND" for that field.
            """
            
            prompt_list = [("text", prompt), ("image_path", local_image_path)] # Use image_path instead of image_url
            ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE, useVertexAI=Constants.IMAGE_MODEL_FREE_VERTEX)
            
            # Parse LLM response
            reg_match = re.search(r'REG_NO:\s*(\d+|NOT_FOUND)', ai_answer)
            ser_match = re.search(r'SER_NO:\s*(\d+|NOT_FOUND)', ai_answer)
            
            reg_no_found = reg_match.group(1) if reg_match and reg_match.group(1) != 'NOT_FOUND' else None
            ser_no_found = ser_match.group(1) if ser_match and ser_match.group(1) != 'NOT_FOUND' else None
            
            return reg_no_found, ser_no_found
            
        except Exception as e:
            print(f"LLM error for {local_image_path}: {e}")
            return None, None


    def generate_md_registration_number(self, plaintiff_id) -> str:
        """
        Generate a new MD registration number for unknown copyrights.
        Format: MDxxxxyyyy where xxxx is plaintiff_id 0-padded to 4 digits, yyyy is consecutive number.

        Args:
            plaintiff_id: The plaintiff ID

        Returns:
            Generated registration number
        """
        # Get existing MD numbers for this plaintiff
        plaintiff_str = f"{plaintiff_id:04d}"
        pattern = f"MD{plaintiff_str}"

        existing_numbers = []
        if len(self.copyright_md_list) > 0:

            # Extract the consecutive numbers
            for reg_no in self.copyright_md_list:
                if len(reg_no) == 10 and reg_no.startswith(pattern):  # MD + 4 digits + 4 digits
                    try:
                        consecutive_num = int(reg_no[-4:])
                        self.copyright_md_list.add(consecutive_num)
                    except ValueError:
                        continue

        # Find next consecutive number
        next_num = 1
        if existing_numbers:
            next_num = max(existing_numbers) + 1

        return f"MD{plaintiff_str}{next_num:04d}"
    
    def update_database_copyright_record(self, tro_process_data: dict, plaintiff_id: int, method: Optional[str] = None) -> bool:
        """Upsert database record into copyrights table"""
        
        try:
            cursor = self.db_conn.cursor()

            # Prepare data for upsert
            tro_value = True

            # Collect all column names and their corresponding values
            columns = [
                "tro", "registration_number", "registration_date", "type_of_work", "title",
                "date_of_creation", "date_of_publication", "copyright_claimant",
                "authorship_on_application", "rights_and_permissions", "description",
                "nation_of_first_publication", "names", "plaintiff_id", "method"
            ]
            
            # Ensure reg_no is present for upsert, as it's the conflict target
            registration_number = tro_process_data.get("reg_no")

            usco_data = {}
            if not registration_number.startswith("MD"):
                usco_search = get_info_from_USCO_using_reg_no(registration_number)

                if usco_search:
                    usco_data = {k: v for k, v in usco_search.items() if v}
                else:
                    print('!!!!', registration_number)
                    data_list = [("text", 'What is the copyright registration number? The registration number always starts with VA or PA (or similar) and 3 groups of digits separated by dashes. You answer following the format: {"is_copyright_registration": "yes", "registration_number": "VA d-ddd-ddd"} or {"is_copyright_registration": "no"}')]
                    data_list.append(("image_path", image_path))


            if not registration_number:
                print(f"Cannot upsert copyright record: 'reg_no' is missing in process_data.")
                return False

            values = [
                tro_value,
                registration_number,
                usco_data.get("registration_date", None),
                usco_data.get("type_of_work", None),
                usco_data.get("title", None),
                usco_data.get("date_of_creation", None),
                usco_data.get("date_of_publication", None),
                usco_data.get("copyright_claimant", None),
                usco_data.get("authorship_on_application", None),
                usco_data.get("rights_and_permissions", None),
                usco_data.get("description", None),
                usco_data.get("nation_of_first_publication", None),
                usco_data.get("names", None),
                plaintiff_id,
                method
            ]

            # Construct the INSERT ... ON CONFLICT DO UPDATE statement
            # Assuming 'registration_number' is unique or has a unique constraint for ON CONFLICT
            insert_columns_str = ", ".join(columns)
            placeholders = ", ".join(["%s"] * len(columns))
            
            update_set_str = ", ".join([f"{col} = EXCLUDED.{col}" for col in columns])
            # Add update_time explicitly
            update_set_str += ", update_time = now()"

            upsert_query = f"""
                INSERT INTO copyrights ({insert_columns_str})
                VALUES ({placeholders})
                ON CONFLICT (registration_number) DO UPDATE SET
                    {update_set_str}
            """
            
            cursor.execute(upsert_query, tuple(values))
            
            self.db_conn.commit()
            return True
        except Exception as e:
            # Use registration_number for error message if available, otherwise use process_data
            identifier = tro_process_data.get("reg_no", "unknown_reg_no")
            print(f"Database upsert error for reg_no {identifier}: {e}")
            print(f"Traceback: {traceback.format_exc()}") # Add traceback for better debugging
            return False
    
    async def copy_file_to_new_name(self, old_filename: str, new_filename: str, plaintiff_id: int) -> bool:
        """Copy file from old name to new name using async_copy_file_with_retry"""

        # TODO: needs to review the logic of the copy location
        try:
            if "_full" not in old_filename: # The certificates are only in high resolution
                old_key = f"plaintiff_images/{plaintiff_id}/low/{old_filename}"
                new_key = f"plaintiff_images/{plaintiff_id}/low/{new_filename}"
                await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key, old_key)
            
            old_key = f"plaintiff_images/{plaintiff_id}/high/{old_filename}"
            new_key = f"plaintiff_images/{plaintiff_id}/high/{new_filename}"
            await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key, old_key)
            
            return True
        except Exception as e:
            print(f"File copy error from {old_filename} to {new_filename}: {e}")
            return False
    
    async def process_copyright_entry(self, copyright_data: Dict, plaintiff_id: int) -> Tuple[bool, Dict]:
        # print(f"\nEntry{'='*100}")
        """Process a single copyright entry and return updated data"""
        original_reg_nos = copyright_data.get('reg_no', [])
        original_full_filenames = copyright_data.get('full_filename', [])

        # double check our assumption
        if not original_full_filenames or len(original_full_filenames) != 1:
            print(f"\nEntry{'='*100}")
            print(f"Skipping copyright entry due to missing or mismatched full_filename: {copyright_data}")
            return False, copyright_data
        
        if not original_reg_nos or len(original_reg_nos) != 1:
            print(f"\nEntry{'='*100}")
            print(f"Skipping copyright entry due to missing or mismatched reg_no: {copyright_data}")
            return False, copyright_data

        
        if original_reg_nos:
            # assuming we only and always has one reg number
            original_reg_no = original_reg_nos[0]
            split_str = split_reg(original_reg_no)

            if len(split_str) == 2:
                if split_str[0] == 'MD': # Internally generated number: MD + 4 digit plaintiff id + 4 digit copyright count
                    formated_reg_no = split_str[0] + split_str[1].zfill(8)
                else: # e.g. VA0123456789
                    formated_reg_no = split_str[0] + split_str[1].zfill(10)
            elif len(split_str) == 3:  # e.g. VAu123456789
                formated_reg_no = split_str[0] + split_str[1].zfill(9)
            else: 
                print(f"!!!!")
                print(f"\033[91mError formated_reg_no not expected: {formated_reg_no} \033[0m")

        else:
            # Generate the new reg no using the IPTrackingManager instance
            formated_reg_no = self.generate_md_registration_number(plaintiff_id)


            
        # Update the copyright with the new reg_no and full_filename
        updated_data = copyright_data.copy()
        updated_data['reg_no'] = formated_reg_no

        final_full_filenames = []
        for original_full_filename in original_full_filenames:
            if re.match(r'^[A-Za-z0-9]+_[A-Za-z]+_full\.webp$', original_full_filename):
                final_full_filenames.append(original_full_filename)
            else:  # Format US_DIS_XXX_XXXX_XXXX_XXXX.web. Same as countring the number of _  => this case is multiple _
                final_full_filenames.append(f"{formated_reg_no}_Exhibit_full.webp")

        updated_data['full_filename'] = final_full_filenames

        # Update the database record with the new reg_no and plaintiff_id
        # Extract method from formated_reg_no
        method = final_full_filenames[0].split('_')[1]

        if not self.update_database_copyright_record(updated_data, plaintiff_id, method):
            print(f"Failed to update database for reg_no {formated_reg_no} and plaintiff_id {plaintiff_id}")
            return False, copyright_data


        return True, updated_data

    async def migrate_case_copyrights(self, case_row: pd.Series) -> bool:
        """Migrate all copyrights for a single case"""
        case_id = case_row['id']
        plaintiff_id = int(float(case_row['plaintiff_id']))
        images_data = case_row.get('images', {})
        copyrights_data = images_data.get('copyrights', {})

        if not copyrights_data:
            return True  # No copyrights to migrate

        updated_copyrights = {}
        files_to_copy = []  # List of (old_filename, new_filename) tuples

        for old_filename, copyright_data in copyrights_data.items():
            # # Check if already migrated
            # if self.is_already_migrated(old_filename):
            #     updated_copyrights[old_filename] = copyright_data
            #     self.skipped_count += 1
            #     continue

            # Process this copyright entry
            success, updated_data = await self.process_copyright_entry(copyright_data, plaintiff_id)
 
            if not success:
                # Keep original data if processing failed
                updated_copyrights[old_filename] = copyright_data
                self.error_count += 1
                continue
            
            if not updated_data:
                print(f"Removing copyright {old_filename} from case {case_id}")
                self.processed_count += 1 # Count as processed, but removed
                continue


            registration_number = updated_data['reg_no']

            if re.match(r'^[A-Za-z0-9]+_[A-Za-z]+\.webp$', old_filename): # Format VA0002445774_GenAI.webp. Same as countring the number of _   => this case is a single _
                new_filename_key = old_filename  
            else: # Format US_DIS_XXX_XXXX_XXXX_XXXX.web. Same as countring the number of _  => this case is multiple _
                new_filename_key = f"{registration_number}_Exhibit.webp"
            
            # Add the main filename to files to copy
            files_to_copy.append((old_filename, new_filename_key))

            # Handle full_filename(s)
            original_full_filenames = copyright_data.get('full_filename', [])
            new_full_filenames = updated_data.get('full_filename', [])

            if len(original_full_filenames) == len(new_full_filenames):
                for i in range(len(original_full_filenames)):
                    old_full = original_full_filenames[i]
                    new_full = new_full_filenames[i]
                    if old_full and new_full:
                        files_to_copy.append((old_full, new_full))
            else:
                print(f"Mismatched original and new full_filenames for {old_filename}. Skipping full_filename copies.")

            # Store with new filename as key
            updated_copyrights[new_filename_key] = updated_data
            self.processed_count += 1

        # Copy all files asynchronously
        copy_tasks = []
        for old_filename, new_filename in files_to_copy:
            task = self.copy_file_to_new_name(old_filename, new_filename, plaintiff_id)
            copy_tasks.append(task)

        if copy_tasks:
            copy_results = await asyncio.gather(*copy_tasks, return_exceptions=True)
            failed_copies = [i for i, result in enumerate(copy_results) if isinstance(result, Exception) or not result]

            if failed_copies:
                print(f"Failed to copy {len(failed_copies)} files for case {case_id}")

        # Update the case data
        case_row['images']['copyrights'] = updated_copyrights

        return True

    async def migrate_all_cases(self, df: pd.DataFrame) -> pd.DataFrame:
        """Migrate all cases in the dataframe"""
        print(f"Starting migration of {len(df)} cases")

        for i, (index, row) in enumerate(df.iterrows()):
            case_id = row.get('id', 'unknown')
            try:
                original_copyright_data = row.get('images', {}).get('copyrights', {})
                deep_copy_original_copyrights = copy.deepcopy(original_copyright_data)

                await self.migrate_case_copyrights(row)

                updated_copyrights_data = row.get('images', {}).get('copyrights', {})

                # Compare original and updated data
                if json.dumps(deep_copy_original_copyrights, sort_keys=True) != json.dumps(updated_copyrights_data, sort_keys=True):
                    # Update the dataframe with the processed row
                    df.loc[index] = row
                    # Save the updated row back to the database immediately
                    insert_and_update_df_to_GZ_batch(df.loc[[index]], "tb_case", key_column="id")
                    print(f"Updated DB for case {case_id}: changes detected.")
                else:
                    print(f"Skipping DB update for case {case_id}: no changes detected in copyrights data.")

                if (i + 1) % 10 == 0:
                    print(f"\033[91mProcessed {i + 1}/{len(df)} cases\033[0m")
                    # Save user answers cache
                    if self.user_answers_cache:
                        self._save_user_answers_cache()
            except Exception as e:
                print(f"Error processing case {case_id}: {e}, traceback: {traceback.format_exc()}")
                self.error_count += 1

        print(f"Migration completed. Processed: {self.processed_count}, Skipped: {self.skipped_count}, Errors: {self.error_count}")
        return df


def main():
    """Main migration function"""
    print("Starting Copyright Filename Migration")
    print("=" * 50)

    # Load cases with copyright images
    print("Loading cases from database...")
    df = get_table_from_GZ("tb_case", force_refresh=False)

    # Filter cases that have copyright images
    cases_with_copyrights = []
    # df = df[df["id"]==5647]
    # sort df by Date_Filed

    df = df.sort_values(by='date_filed', ascending=True)
    existing_md_copyright_list = set()
    for index, row in df.iterrows():
        images_data = row.get('images', {})
        if images_data and 'copyrights' in images_data and images_data['copyrights']:
            cases_with_copyrights.append(index)
            for image_filename, related_info in images_data['copyrights'].items():
                unparsed_reg_nos = related_info.get("reg_no", "")

                # standardize the value by assigning a list to the reg_no
                if not unparsed_reg_nos: # handle "", None, []
                    unparsed_reg_nos = []
                elif isinstance(unparsed_reg_nos, str):
                    unparsed_reg_nos = [unparsed_reg_nos]
                elif isinstance(unparsed_reg_nos, list) and not unparsed_reg_nos[0]:
                    unparsed_reg_nos = []

                related_info['reg_no'] = unparsed_reg_nos

                # TODO: Check-in Assume the reg_no is a list with only one element
                if unparsed_reg_nos:
                    unparsed_reg_no = unparsed_reg_nos[0]
                else:
                    continue
                
                # save the formated reg_no to the existing_md_copyright_list
                split_str = split_reg(unparsed_reg_no)
                if len(split_str) == 2 and split_str[0] == 'MD':
                    formated_reg_no = split_str[0] + split_str[1].zfill(8)
                    existing_md_copyright_list.add(formated_reg_no)

    if not cases_with_copyrights:
        print("No cases with copyright images found.")
        return

    # TODO: do the first 5 for the purpose of testing
    # df_to_migrate = df.loc[cases_with_copyrights].copy()
    df_to_migrate = df.loc[cases_with_copyrights].copy()[:5]
    print(f"Found {len(df_to_migrate)} cases with copyright images")

    # Confirm migration
    # response = input(f"Proceed with migration of {len(df_to_migrate)} cases? (y/N): ")
    # if response.lower() != 'y':
    #     print("Migration cancelled.")
    #     return
    
    
     
    # Perform migration
    async def run_migration():
        async with CopyrightFilenameMigrator(existing_md_copyright_list) as migrator:
            updated_df = await migrator.migrate_all_cases(df_to_migrate)
            return updated_df

    # Run the async migration
    asyncio.run(run_migration())

    print("Migration completed successfully!")


if __name__ == "__main__":
    main()

# VA not working
# Can not repeatly save the location