import uuid

def generate_uuid(ser_no):
    """Generate consistent UUID from ser_no (TM) or rego_no (CP) or filename (PT) using UUID version 5 (SHA-1 hash)"""
    if not isinstance(ser_no, str):
        ser_no = str(ser_no)
    # Use uuid.uuid5 to generate a UUID from a namespace UUID and a name (ser_no)
    # uuid.NAMESPACE_OID is a predefined namespace UUID.
    # This ensures consistency: the same ser_no will always produce the same UUID.
    return str(uuid.uuid5(uuid.NAMESPACE_OID, ser_no))

if __name__ == "__main__":
    print(generate_uuid("87323957"))
