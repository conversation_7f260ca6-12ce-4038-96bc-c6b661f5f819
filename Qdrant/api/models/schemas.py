"""
Pydantic models for request and response validation.
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

# Constants for vector dimensions - Using single SigLIP model for all embeddings
SIGLIP_DIMENSION = 1024

# Request models
class ProductImage(BaseModel):
    """Model for a product image with its SigLIP vector embedding."""
    id: Optional[str] = None
    siglip_vector: List[float]
    filename: Optional[str] = None  # For forward check metadata storage

class ForwardCheckRequest(BaseModel):
    """Request model for the forward check endpoint."""
    client_id: str
    check_id: str
    products: List[ProductImage]
    search_ip_type: Optional[str] = None  # Optional IP type filter
    threshold: Optional[float] = None  # Optional similarity threshold
    threshold_text: Optional[float] = None  # Optional similarity threshold for text matches
    top_n: Optional[int] = None  # Optional top N results limit

class IPAsset(BaseModel):
    """Model for an IP asset with its metadata and SigLIP vector embedding."""
    ip_type: str
    id: str
    metadata: Dict[str, Any]
    siglip_vector: List[float]

class ReverseCheckRequest(BaseModel):
    """Request model for the reverse check endpoint."""
    ip_assets: List[IPAsset]
    threshold: Optional[float] = None  # Optional similarity threshold
    top_n: Optional[int] = None  # Optional top N results limit

class DeletePointsRequest(BaseModel):
    """Request model for the delete points endpoint."""
    collection_name: str
    point_ids: List[str]

# Response models
class Infringement(BaseModel):
    """Model for an infringement result."""
    ip_type: str
    ip_asset_id: str
    score: float
    qdrant_metadata: Optional[Any] = None # Payload from Qdrant
    db_metadata: Optional[Any] = None # Record from database
    query_filename: Optional[str] = None
    data_type: Optional[str] = None  # Image or Text

class ForwardCheckResponse(BaseModel):
    """Response model for the forward check endpoint."""
    results: List[Infringement]

class ProductPointInfringement(BaseModel):
    """Model for product point infringement results."""
    product_point_id: str
    client_id: str
    check_id: str
    score: float

class IPAssetInfringement(BaseModel):
    """Model for IP asset infringement results."""
    input_ip_asset_id: str
    potential_infringements: List[ProductPointInfringement]

class ReverseCheckResponse(BaseModel):
    """Response model for the reverse check endpoint."""
    results: List[IPAssetInfringement]

class DeletePointsResponse(BaseModel):
    """Response model for the delete points endpoint."""
    status: str
    operation_id: int
    result: bool
