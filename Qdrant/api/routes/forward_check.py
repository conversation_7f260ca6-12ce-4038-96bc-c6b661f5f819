"""
Forward check endpoint for comparing product images against IP assets.
"""

from fastapi import APIRouter, Depends

from models.schemas import ForwardCheckRequest, ForwardCheckResponse, Infringement
from utils.auth import verify_token
from utils.db import get_ip_asset_metadata
from services.qdrant_service import upsert_product_images, query_siglip_vector
import uuid

router = APIRouter()

APP_NAMESPACE = uuid.UUID('f81d4fae-7dec-11d0-a765-00a0c91e6bf6') # Example namespace


@router.post("/forward_check", response_model=ForwardCheckResponse, dependencies=[Depends(verify_token)])
async def forward_check(request: ForwardCheckRequest):
    """
    Forward Check endpoint.
    Compare user-submitted product images against the IP assets database.

    Args:
        request: The forward check request containing:
            - client_id: The client identifier
            - check_id: The check identifier
            - products: List of products with embeddings
            - search_ip_type: Optional IP type to filter search results ("Copyright", "Patent", "Trademark").
                            If not provided, all IP types will be searched.
            - threshold: Optional similarity threshold override for filtering results
            - top_n: Optional limit for maximum number of results per IP type

    Returns:
        The forward check response.
    """
    # 1. Save the product pictures embeddings in the Product_Images collection for reverse check
    products = []
    for product in request.products:
        products.append({
            "siglip_vector": product.siglip_vector,
            "filename": product.filename
        })
    upsert_product_images(request.client_id, request.check_id, products)

    # 2. Qdrant search
    # Determine which IP types to search based on search_ip_type parameter
    ip_types_to_search = []
    if request.search_ip_type:
        if request.search_ip_type == "Patent":
            ip_types_to_search.append((request.search_ip_type, request.threshold, request.top_n * 5, "image"))  # Why * 5, because there can be 5 match for a single patent
            ip_types_to_search.append((request.search_ip_type, request.threshold_text, request.top_n, "text"))
        else:
            ip_types_to_search.append((request.search_ip_type, request.threshold, request.top_n, "image"))
    else:
        ip_types_to_search = [("Copyright", request.threshold, request.top_n, "image"), ("Trademark", request.threshold, request.top_n, "image"),
                                ("Patent", request.threshold, request.top_n * 5, "image"), ("Patent", request.threshold_text, request.top_n, "text")]
        
    # Perform the search
    all_results = []    
    for product in request.products:
        # Search for each IP type
        for ip_type, threshold, top_n, data_type in ip_types_to_search:
            print(f"Searching for {ip_type} with threshold {threshold} and data_type {data_type} and top_n {top_n}")
            type_results = query_siglip_vector(
                collection_name="IP_Assets",
                query_vector=product.siglip_vector,
                min_score_threshold=threshold,
                max_results=top_n,
                ip_type_filter=ip_type,
                data_type=data_type,
                query_filename=product.filename
            )
            all_results.extend(type_results)

    # Collect unique IP_Assets Point IDs and get info from Database
    candidate_ids = set([result["id"] for result in all_results])
    ip_asset_metadata = get_ip_asset_metadata(candidate_ids)

    # Process results and apply filtering logic
    potential_infringements = []
    for result in all_results:
        db_metadata = None
        qdrant_metadata = None
        if result["id"] in ip_asset_metadata:
            db_metadata = ip_asset_metadata[result["id"]]["data"]
        if result["payload"].get("plaintiff_id"):
            qdrant_metadata = result["payload"]

        # Get IP type from result payload or metadata
        if result["payload"].get("ip_type"):
            ip_type = result["payload"]["ip_type"]
        elif result["id"] in ip_asset_metadata:
            ip_type = ip_asset_metadata[result["id"]]["type"].capitalize()
        else:
            print(f"IP type not found. Result[id]={result['id']}, Payload={result['payload']}, IP_Asset_Metadata={ip_asset_metadata}")
            continue

        # Create infringement entry
        infringement_entry = Infringement(
            ip_type=ip_type,
            ip_asset_id=result["id"], 
            score=result["score"], 
            qdrant_metadata=qdrant_metadata,
            db_metadata=db_metadata, 
            query_filename=result["query_filename"],
            data_type=result["data_type"] 
        )

        # Check if this IP asset is already in potential_infringements
        existing = next((inf for inf in potential_infringements if inf.ip_asset_id == result["id"]), None)
        if existing:
            # Update score if higher
            if result["score"] > existing.score:
                existing.score = result["score"]
        else:
            potential_infringements.append(infringement_entry)

    # Patent Text analysis: ("text" in qdrant_metadata but "patent_title" in db_metadata"): Group infringements by text and keep only the one with the highest score
    if request.search_ip_type == "Patent":
        patent_infringements_by_text = {}
        for inf in potential_infringements:
            if inf.qdrant_metadata and inf.qdrant_metadata.get("text"):
                text_identifier = inf.qdrant_metadata.get("text")
            elif inf.db_metadata and inf.db_metadata.get("patent_title"):
                text_identifier = inf.db_metadata.get("patent_title")
            else:
                continue

            if text_identifier not in patent_infringements_by_text or inf.score > patent_infringements_by_text[text_identifier].score:
                patent_infringements_by_text[text_identifier] = inf
        
        potential_infringements = list(patent_infringements_by_text.values())
    
    # Sort by score in descending order
    potential_infringements.sort(key=lambda x: x.score, reverse=True)
    return ForwardCheckResponse(results=potential_infringements)
