"""
Qdrant service for vector search operations.
"""

from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.http import models
from typing import List, Dict, Any
import uuid
import os

qdrant_client = QdrantClient(url=os.getenv("QDRANT_URL", "https://vectorstore1.maidalv.com:6333"), api_key=os.getenv("QDRANT_API_KEY"), https=True)

# if os.name != 'nt':
#     # Config in the docker
#     qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, https=True)    
# else:
#     # Config in local
#     qdrant_client = QdrantClient(url=os.environ["QDRANT_API_URL"], api_key=QDRANT_API_KEY)

def generate_product_point_id(check_id: str, image_path: str) -> str:
    """
    Generates a deterministic UUIDv5 for a given check_id and image_path.
    This ensures that the same image for the same check always gets the same ID.
    """
    # Create a stable, unique string for the asset.
    unique_name = f"{check_id}{image_path}"
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, unique_name))

def upsert_product_images(client_id: str, check_id: str, products: List[Dict[str, Any]]):
    """
    Upsert product images into the Product_Images collection.

    Args:
        client_id: The client ID.
        check_id: The check ID.
        products: A list of product image data.

    Returns:
        The result of the upsert operation.
    """
    points = []
    for product in products:
        # Create point
        point = models.PointStruct(
            id=generate_product_point_id(check_id, product.get("filename")),
            vector={
                "siglip_vector": product.get("siglip_vector")
            },
            payload={
                "client_id": client_id,
                "check_id": check_id,
                "filename": product.get("filename")  # Store filename for forward check metadata
            }
        )
        points.append(point)

    # Upsert points into Product_Images collection
    return qdrant_client.upsert(
        collection_name="Product_Images",
        points=points
    )

def upsert_ip_assets(ip_assets: List[Dict[str, Any]]):
    """
    Upsert IP assets into the IP_Assets collection.

    Args:
        ip_assets: A list of IP asset data.

    Returns:
        The result of the upsert operation.
    """
    points = []
    for ip_asset in ip_assets:
        # Create point with single SigLIP vector
        point = models.PointStruct(
            id=ip_asset.get("id"),
            vector={
                "siglip_vector": ip_asset.get("siglip_vector", [])
            },
            payload={
                "ip_type": ip_asset.get("ip_type"),
                "metadata": ip_asset.get("metadata", {})
            }
        )
        points.append(point)

    # Upsert points into IP_Assets collection
    return qdrant_client.upsert(
        collection_name="IP_Assets",
        points=points
    )

def delete_points(collection_name: str, point_ids: List[str]):
    """
    Delete points from a collection.

    Args:
        collection_name: The name of the collection.
        point_ids: A list of point IDs to delete.

    Returns:
        The result of the delete operation.
    """
    return qdrant_client.delete(
        collection_name=collection_name,
        points_selector=models.PointIdsList(points=point_ids)
    )

# def query_batch_points(collection_name: str, requests: List[Dict[str, Any]]):
#     """
#     Execute a batch query against a collection using individual queries.

#     Since Qdrant's batch query API can be unreliable, this function executes
#     individual queries and returns the results in the same format as a batch query.

#     Args:
#         collection_name: The name of the collection.
#         requests: A list of query request dictionaries with keys:
#                  - query_vector: tuple of (vector_name, vector_values)
#                  - limit: maximum number of results
#                  - score_threshold: minimum score threshold
#                  - with_payload: payload selector
#                  - with_vectors: whether to include vectors

#     Returns:
#         A list of query results, one for each request.
#     """
#     results = []

#     for request in requests:
#         # Extract parameters from request
#         query_vector = request.get("query_vector")
#         limit = request.get("limit", 10)
#         score_threshold = request.get("score_threshold", 0.0)
#         with_payload = request.get("with_payload", True)
#         with_vectors = request.get("with_vectors", False)

#         # Execute individual query using the existing search method
#         query_result = qdrant_client.search(
#             collection_name=collection_name,
#             query_vector=query_vector,
#             limit=limit,
#             score_threshold=score_threshold,
#             with_payload=with_payload,
#             with_vectors=with_vectors
#         )

#         results.append(query_result)

#     return results



def query_batch_points(collection_name: str, search_queries: List[models.QueryRequest]):
    return qdrant_client.query_batch_points(collection_name=collection_name, requests=search_queries)

def query_with_dynamic_limit(collection_name: str, vector_name: str, query_vector: List[float],
                            min_score_threshold: float = 0.5, max_results: int = 200):
    """
    Execute a query with a dynamic limit based on score threshold.

    Args:
        collection_name: The name of the collection.
        vector_name: The name of the vector to query against.
        query_vector: The query vector.
        min_score_threshold: The minimum score threshold to include results.
        max_results: The maximum number of results to return.

    Returns:
        The filtered query results.
    """
    # First, retrieve a larger set of results
    results = qdrant_client.search(
        collection_name=collection_name,
        query_vector=(vector_name, query_vector),
        limit=max_results,  # Get a larger initial set
        score_threshold=min_score_threshold,  # Apply a minimum threshold
        with_payload=True,
        with_vectors=False
    )

    return results

def query_siglip_vector(collection_name: str, query_vector: List[float],
                       min_score_threshold: float = 0.5, max_results: int = 200,
                       ip_type_filter: str = None, data_type: str = None, query_filename: str = None):
    """
    Execute a query using SigLIP vector embedding.

    Args:
        collection_name: The name of the collection.
        query_vector: The SigLIP query vector.
        min_score_threshold: The minimum score threshold to include results.
        max_results: The maximum number of results to return.
        ip_type_filter: Optional filter for specific IP type (Copyright, Patent, Trademark).
        data_type: Optional filter for data type ("image", "text").

    Returns:
        The filtered query results.
    """
    # Build filter conditions
    must_conditions = []
    if ip_type_filter:
        must_conditions.append(models.FieldCondition(key="ip_type", match=models.MatchValue(value=ip_type_filter)))
    if ip_type_filter == "Patent" and data_type:
        must_conditions.append(models.FieldCondition(key="data_type", match=models.MatchValue(value=data_type)))

    filter_conditions = None
    if must_conditions:
        filter_conditions = models.Filter(must=must_conditions)

    # Query with SigLIP vector
    results = qdrant_client.search(
        collection_name=collection_name,
        query_vector=("siglip_vector", query_vector),
        limit=max_results,
        score_threshold=min_score_threshold,
        query_filter=filter_conditions,
        with_payload=True,
        with_vectors=False
    )
    
    # Create a new dictionary with the data_type field
    results_with_data_type = []
    for result in results:
        result_dict = {
            "id": result.id,
            "score": result.score,
            "payload": result.payload,
            "data_type": data_type,  # Add the data_type field
            "query_filename": query_filename
        }
        results_with_data_type.append(result_dict)

    return results_with_data_type
