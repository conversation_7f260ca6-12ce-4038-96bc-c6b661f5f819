import os
from datetime import timedelta
from celery.schedules import crontab

class CeleryConfig:
    # Celery settings
    broker_url = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    result_backend = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    
    # Timezone settings
    timezone = 'America/New_York'
    enable_utc = True
    
    # Task serialization
    task_serializer = 'json'
    accept_content = ['json']
    result_serializer = 'json'
    result_expires = 3600
    
    # Worker settings
    worker_prefetch_multiplier = 1
    task_acks_late = True
    worker_max_tasks_per_child = 1000
    
    # Beat scheduler
    beat_scheduler = 'celery.beat:PersistentScheduler'
    
    # Beat schedule for periodic tasks
    beat_schedule = {
        'daily-case-fetch': {
            'task': 'Scheduler_Celery.tasks.daily_case_fetch_task',
            'schedule': crontab(hour=0, minute=30),  # 00:30 every day NY time
            'options': {'timezone': 'America/New_York'}
        },
    }

class EmailConfig:
    # Email settings for reports
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME', '<EMAIL>')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD', 'idux lymv xraj woso')
    
    # Report recipients
    REPORT_RECIPIENTS = os.environ.get('REPORT_RECIPIENTS', '').split(',') if os.environ.get('REPORT_RECIPIENTS') else []