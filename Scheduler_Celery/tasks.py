from .celery_app import celery_app
from .utils.email_utils import send_daily_report_email, send_error_notification_email
import logging
from datetime import datetime
import traceback
import pandas as pd

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@celery_app.task(bind=True, name='Scheduler_Celery.tasks.daily_case_fetch_task')
def daily_case_fetch_task(self, num_days=1):
    """
    Daily task to fetch new cases and send email report
    Runs every day at 00:30 NY time
    """
    try:
        logger.info(f"Starting daily case fetch task with {num_days} days")
        
        # Update task status
        self.update_state(state='PROGRESS', meta={'status': 'Starting case fetch...'})
        
        # Import your workflow manager
        from Alerts.WorkflowManager import fetch
        
        # Store initial state for reporting
        self.update_state(state='PROGRESS', meta={'status': 'Fetching new cases...'})
        
        # Execute the fetch workflow
        result = fetch(num_days)
        
        # Get case statistics
        stats = get_case_statistics(result)
        
        # Update task status
        self.update_state(state='PROGRESS', meta={'status': 'Sending email report...'})
        
        # Send email report
        send_daily_report_email(stats)
        
        logger.info("Daily case fetch task completed successfully")
        
        return {
            'status': 'completed',
            'timestamp': datetime.now().isoformat(),
            'stats': stats,
            'message': 'Daily case fetch completed successfully'
        }
        
    except Exception as exc:
        logger.error(f"Daily case fetch task failed: {str(exc)}")
        logger.error(traceback.format_exc())
        
        # Send error notification email
        send_error_notification_email(str(exc), "Daily Case Fetch")
        
        # Update task state to failure
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(exc),
                'traceback': traceback.format_exc()
            }
        )
        raise exc

def get_case_statistics(result):
    """
    Extract statistics from the fetch result
    The fetch function returns a DataFrame from scrape_date_range with columns:
    - title, date_filed, court, docket, ln_url
    """
    try:
        if isinstance(result, pd.DataFrame):
            total_cases = len(result)
            # For newly scraped cases, we don't have IP information yet
            # IP analysis happens during the reprocessing step
            # So for now, we'll report all new cases as "pending IP analysis"
            missing_ip_count = total_cases  # All new cases need IP analysis

            logger.info(f"Case statistics: {total_cases} new cases found, all pending IP analysis")
        else:
            # If result is not a DataFrame, provide default values
            total_cases = 0
            missing_ip_count = 0
            logger.warning(f"Expected DataFrame but got {type(result)}")

        return {
            'total_new_cases': total_cases,
            'missing_ip_count': missing_ip_count,
            'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'case_details': result.to_dict('records') if isinstance(result, pd.DataFrame) and not result.empty else []
        }
    except Exception as e:
        logger.error(f"Error getting case statistics: {str(e)}")
        return {
            'total_new_cases': 0,
            'missing_ip_count': 0,
            'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': str(e)
        }

@celery_app.task
def test_task():
    """Simple test task to verify Celery is working"""
    logger.info("Test task executed successfully")
    return "Test task completed successfully!"

# Task monitoring signals
from celery.signals import task_prerun, task_postrun, task_failure

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    logger.info(f'Task {task.name} started: {task_id}')

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    logger.info(f'Task {task.name} completed: {task_id} - State: {state}')

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    logger.error(f'Task {sender.name} failed: {task_id} - Exception: {exception}')