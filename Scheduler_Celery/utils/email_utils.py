import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime
import logging
from ..config import EmailConfig

logger = logging.getLogger(__name__)

def send_daily_report_email(stats):
    """Send daily report email using SMTP"""
    try:
        if not EmailConfig.REPORT_RECIPIENTS or not EmailConfig.MAIL_USERNAME:
            logger.warning("Email configuration missing. Skipping email report.")
            return
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = EmailConfig.MAIL_USERNAME
        msg['To'] = ', '.join(EmailConfig.REPORT_RECIPIENTS)
        msg['Subject'] = f"Daily Case Fetch Report - {stats['fetch_date']}"
        
        # Email body
        body = f"""
            Daily Case Fetch Report
            ======================

            Date: {stats['fetch_date']}
            Total New Cases: {stats['total_new_cases']}
            Cases Pending IP Analysis: {stats['missing_ip_count']}

            The daily case fetch workflow has completed successfully.
            New cases have been identified and are now being processed for IP asset analysis.

            ---
            Automated report from Case Management System
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        server = smtplib.SMTP(EmailConfig.MAIL_SERVER, EmailConfig.MAIL_PORT)
        if EmailConfig.MAIL_USE_TLS:
            server.starttls()
        server.login(EmailConfig.MAIL_USERNAME, EmailConfig.MAIL_PASSWORD)
        text = msg.as_string()
        server.sendmail(EmailConfig.MAIL_USERNAME, EmailConfig.REPORT_RECIPIENTS, text)
        server.quit()
        
        logger.info("Daily report email sent successfully")
        
    except Exception as e:
        logger.error(f"Failed to send daily report email: {str(e)}")

def send_error_notification_email(error_msg, task_name="Daily Case Fetch"):
    """Send error notification email"""
    try:
        if not EmailConfig.REPORT_RECIPIENTS or not EmailConfig.MAIL_USERNAME:
            logger.warning("Email configuration missing. Skipping error notification.")
            return
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = EmailConfig.MAIL_USERNAME
        msg['To'] = ', '.join(EmailConfig.REPORT_RECIPIENTS)
        msg['Subject'] = f"{task_name} - ERROR"
        
        # Email body
        body = f"""
            {task_name} Error
            {'=' * (len(task_name) + 6)}

            The {task_name.lower()} task has failed with the following error:

            {error_msg}

            Please check the application logs for more details.

            ---
            Automated error notification from Case Management System
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        server = smtplib.SMTP(EmailConfig.MAIL_SERVER, EmailConfig.MAIL_PORT)
        if EmailConfig.MAIL_USE_TLS:
            server.starttls()
        server.login(EmailConfig.MAIL_USERNAME, EmailConfig.MAIL_PASSWORD)
        text = msg.as_string()
        server.sendmail(EmailConfig.MAIL_USERNAME, EmailConfig.REPORT_RECIPIENTS, text)
        server.quit()
        
        logger.info("Error notification email sent")
        
    except Exception as e:
        logger.error(f"Failed to send error notification email: {str(e)}")