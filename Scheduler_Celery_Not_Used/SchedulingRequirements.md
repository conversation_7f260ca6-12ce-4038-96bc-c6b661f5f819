1. New cases: Every day at 00.30 NY time: workflowmanager->fetch (1 day) : open every case (open webpage for every result) to deside which one we want to capture (based on title and steps text)
   1. Email report sent: How many new cases, how many missing IP
2. Case update (e.g. title, status, and steps)and missed cases (because schedule A was not in the steps yet):
   1. When:
      1. Every Saturday (1.30am NY): search last week,
      2. Every 1st sunday of the month (1.30am NY): search last month
      3. Every 2nd sunday of the month (1.30am NY): search 2 months ago
      4. Every 3rd sunday of the month (1.30am NY): search 3 months ago
   2. What:
      1. open every case to deside which one we want to capture based on steps text, except cases that are already marked as closed in the dataframe
      2. update steps and all details fields (like defendant name and laywers and status (open or close)))
      3. Download more IP: only if IPManager says that some IP is missing => reprocess with 'processing_mode': 'resume' (resume mode might need a little more testing)
   3. Email report:
      1. How many new cases,
      2. how many titles have changed,
      3. how many closed,
      4. how many got more steps,
      5. how many cases where missing IP and got more IP (and from where)
      6. how many cases where missing IP before and after the processing
3. Case update and missed cases (for cases older than 3 month):
   1. When: 4th Sunday of the month
   2. What:
      1. All open cases already in Database more than 3 month old but less than 18 months old   (we only open cases that are already in the dataframe and still open)
      2. update steps and all details fields (like defendant name and laywers and status (open or close)))
      3. Download more IP: only if IPManager says that some IP is missing => reprocess with 'processing_mode': 'resume'
   3. Email report: same as above
4. Stats: every Saturday at 10 am NY: refresh the stats (same as the button in the UI , which is in StatisticsCollector.py I think)
5. Trademarks: daily (tuesday to sartuday, or just daily to confirm there is nothing on saturday and sunday) at 1pm NY time. Trademarks_Bulk -> get_bulk_trademarks for the day before  (or catch up since the last day in the database)
6. Patents: every wednesday at 5pm NY time. Patents_Bulk -> Weekly patents   (the file come out every tuesday)
